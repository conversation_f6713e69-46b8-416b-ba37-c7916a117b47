-- Remove run-related columns from feedbacks and feedbacks_rmt tables

ALTER TABLE feedbacks_rmt
DROP COLUMN IF EXISTS name,
DROP COLUMN IF EXISTS end_time,
DROP COLUMN IF EXISTS run_type,
DROP COLUMN IF EXISTS parent_run_id,
DROP COLUMN IF EXISTS reference_example_id,
DROP COLUMN IF EXISTS reference_dataset_id,
DROP COLUMN IF EXISTS manifest_id,
DROP COLUMN IF EXISTS status,
DROP COLUMN IF EXISTS dotted_order,
DROP COLUMN IF EXISTS prompt_tokens,
DROP COLUMN IF EXISTS completion_tokens,
DROP COLUMN IF EXISTS total_tokens,
DROP COLUMN IF EXISTS first_token_time,
DROP COLUMN IF EXISTS received_at,
DROP COLUMN IF EXISTS tenant_tier,
DROP COLUMN IF EXISTS inserted_at,
DROP COLUMN IF EXISTS prompt_cost,
DROP COLUMN IF EXISTS completion_cost,
DROP COLUMN IF EXISTS total_cost,
DROP COLUMN IF EXISTS trace_tier,
DROP COLUMN IF EXISTS trace_ttl_seconds,
DROP COLUMN IF EXISTS trace_first_received_at,
DROP COLUMN IF EXISTS trace_upgrade,
DROP COLUMN IF EXISTS thread_id,
DROP COLUMN IF EXISTS tags,
DROP COLUMN IF EXISTS metadata_kv,
DROP COLUMN IF EXISTS inputs_kv,
DROP COLUMN IF EXISTS outputs_kv,
DROP COLUMN IF EXISTS expiration_time;

ALTER TABLE feedbacks
DROP COLUMN IF EXISTS name,
DROP COLUMN IF EXISTS end_time,
DROP COLUMN IF EXISTS run_type,
DROP COLUMN IF EXISTS parent_run_id,
DROP COLUMN IF EXISTS reference_example_id,
DROP COLUMN IF EXISTS reference_dataset_id,
DROP COLUMN IF EXISTS manifest_id,
DROP COLUMN IF EXISTS status,
DROP COLUMN IF EXISTS dotted_order,
DROP COLUMN IF EXISTS prompt_tokens,
DROP COLUMN IF EXISTS completion_tokens,
DROP COLUMN IF EXISTS total_tokens,
DROP COLUMN IF EXISTS first_token_time,
DROP COLUMN IF EXISTS received_at,
DROP COLUMN IF EXISTS tenant_tier,
DROP COLUMN IF EXISTS inserted_at,
DROP COLUMN IF EXISTS prompt_cost,
DROP COLUMN IF EXISTS completion_cost,
DROP COLUMN IF EXISTS total_cost,
DROP COLUMN IF EXISTS trace_tier,
DROP COLUMN IF EXISTS trace_ttl_seconds,
DROP COLUMN IF EXISTS trace_first_received_at,
DROP COLUMN IF EXISTS trace_upgrade,
DROP COLUMN IF EXISTS thread_id,
DROP COLUMN IF EXISTS tags,
DROP COLUMN IF EXISTS metadata_kv,
DROP COLUMN IF EXISTS inputs_kv,
DROP COLUMN IF EXISTS outputs_kv,
DROP COLUMN IF EXISTS expiration_time;

-- Also remove columns from feedbacks_rmt_id table
ALTER TABLE feedbacks_rmt_id
DROP COLUMN IF EXISTS name,
DROP COLUMN IF EXISTS end_time,
DROP COLUMN IF EXISTS run_type,
DROP COLUMN IF EXISTS parent_run_id,
DROP COLUMN IF EXISTS reference_example_id,
DROP COLUMN IF EXISTS reference_dataset_id,
DROP COLUMN IF EXISTS manifest_id,
DROP COLUMN IF EXISTS status,
DROP COLUMN IF EXISTS dotted_order,
DROP COLUMN IF EXISTS prompt_tokens,
DROP COLUMN IF EXISTS completion_tokens,
DROP COLUMN IF EXISTS total_tokens,
DROP COLUMN IF EXISTS first_token_time,
DROP COLUMN IF EXISTS received_at,
DROP COLUMN IF EXISTS tenant_tier,
DROP COLUMN IF EXISTS inserted_at,
DROP COLUMN IF EXISTS prompt_cost,
DROP COLUMN IF EXISTS completion_cost,
DROP COLUMN IF EXISTS total_cost,
DROP COLUMN IF EXISTS trace_tier,
DROP COLUMN IF EXISTS trace_ttl_seconds,
DROP COLUMN IF EXISTS trace_first_received_at,
DROP COLUMN IF EXISTS trace_upgrade,
DROP COLUMN IF EXISTS thread_id,
DROP COLUMN IF EXISTS tags,
DROP COLUMN IF EXISTS metadata_kv,
DROP COLUMN IF EXISTS inputs_kv,
DROP COLUMN IF EXISTS outputs_kv,
DROP COLUMN IF EXISTS expiration_time;

-- Recreate materialized views to ensure they work with the original schema
-- Drop existing materialized views
DROP VIEW IF EXISTS feedbacks_rmt_mv {{ON_CLUSTER}};
DROP VIEW IF EXISTS feedbacks_rmt_id_mv {{ON_CLUSTER}};

-- Recreate materialized views
CREATE MATERIALIZED VIEW IF NOT EXISTS feedbacks_rmt_mv {{ON_CLUSTER}} TO feedbacks_rmt
AS
SELECT * FROM feedbacks;

CREATE MATERIALIZED VIEW IF NOT EXISTS feedbacks_rmt_id_mv {{ON_CLUSTER}} TO feedbacks_rmt_id
AS
SELECT * FROM feedbacks;
