-- Remove the indexes added to feedbacks_rmt and runs_lite tables

-- Remove indexes from feedbacks_rmt table
ALTER TABLE feedbacks_rmt
DROP INDEX IF EXISTS idx_value_tokenbf,
DROP INDEX IF EXISTS idx_run_id_bloom,
DROP INDEX IF EXISTS sk_mm_modified_at;

-- Remove indexes from runs_lite table
ALTER TABLE runs_lite
DROP INDEX IF EXISTS idx_value_tokenbf,
DROP INDEX IF EXISTS idx_run_id_bloom,
DROP INDEX IF EXISTS idx_run_type_bloom,
DROP INDEX IF EXISTS idx_status_bloom,
DROP INDEX IF EXISTS sk_mm_run_start_time,
DROP INDEX IF EXISTS sk_mm_run_end_time;
