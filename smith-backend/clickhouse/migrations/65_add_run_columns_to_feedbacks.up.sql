-- Add run-related columns to feedbacks and feedbacks_rmt tables
-- These columns will allow feedback tables to contain run metadata for better querying

ALTER TABLE feedbacks_rmt
ADD COLUMN IF NOT EXISTS name String DEFAULT '' COMMENT 'Name of the run',
ADD COLUMN IF NOT EXISTS end_time Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'End time of the run with microsecond precision',
ADD COLUMN IF NOT EXISTS run_type LowCardinality(String) DEFAULT '' COMMENT 'Type of the run',
ADD COLUMN IF NOT EXISTS parent_run_id Nullable(UUID) DEFAULT NULL COMMENT 'Parent run ID',
ADD COLUMN IF NOT EXISTS reference_example_id Nullable(UUID) DEFAULT NULL COMMENT 'Reference example ID',
ADD COLUMN IF NOT EXISTS reference_dataset_id Nullable(UUID) DEFAULT NULL COMMENT 'Reference dataset ID',
ADD COLUMN IF NOT EXISTS manifest_id Nullable(UUID) DEFAULT NULL COMMENT 'Manifest ID',
ADD COLUMN IF NOT EXISTS status LowCardinality(String) DEFAULT '' COMMENT 'Status of the run',
ADD COLUMN IF NOT EXISTS dotted_order String DEFAULT '' COMMENT 'Dotted order for hierarchical runs',
ADD COLUMN IF NOT EXISTS prompt_tokens UInt32 DEFAULT 0 COMMENT 'Number of prompt tokens',
ADD COLUMN IF NOT EXISTS completion_tokens UInt32 DEFAULT 0 COMMENT 'Number of completion tokens',
ADD COLUMN IF NOT EXISTS total_tokens UInt32 DEFAULT 0 COMMENT 'Total number of tokens',
ADD COLUMN IF NOT EXISTS first_token_time Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'Time of first token with microsecond precision',
ADD COLUMN IF NOT EXISTS received_at Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'Time of receiving the run with microsecond precision',
ADD COLUMN IF NOT EXISTS tenant_tier UInt8 DEFAULT 0 COMMENT 'Tier of the tenant',
ADD COLUMN IF NOT EXISTS inserted_at DateTime64(6, 'UTC') DEFAULT now64(6, 'UTC') COMMENT 'Time of insertion with microsecond precision',
ADD COLUMN IF NOT EXISTS prompt_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of prompt tokens',
ADD COLUMN IF NOT EXISTS completion_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of completion tokens',
ADD COLUMN IF NOT EXISTS total_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of all tokens',
ADD COLUMN IF NOT EXISTS trace_tier Nullable(String) DEFAULT NULL COMMENT 'Tier of the trace',
ADD COLUMN IF NOT EXISTS trace_ttl_seconds Nullable(UInt64) DEFAULT NULL COMMENT 'Time to live in seconds',
ADD COLUMN IF NOT EXISTS trace_first_received_at Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'Time when the parent run was first received',
ADD COLUMN IF NOT EXISTS trace_upgrade Bool DEFAULT false COMMENT 'If trace was upgraded',
ADD COLUMN IF NOT EXISTS thread_id Nullable(String) DEFAULT NULL COMMENT 'Thread ID for the run',
ADD COLUMN IF NOT EXISTS tags Array(String) DEFAULT [] COMMENT 'Tags for the run',
ADD COLUMN IF NOT EXISTS metadata_kv Map(String, String) DEFAULT map() COMMENT 'Metadata key-value pairs',
ADD COLUMN IF NOT EXISTS inputs_kv Map(String, String) DEFAULT map() COMMENT 'Inputs key-value pairs',
ADD COLUMN IF NOT EXISTS outputs_kv Map(String, String) DEFAULT map() COMMENT 'Outputs key-value pairs',
ADD COLUMN IF NOT EXISTS expiration_time Nullable(DateTime) DEFAULT NULL COMMENT 'Time when the run expires';

ALTER TABLE feedbacks
ADD COLUMN IF NOT EXISTS name String DEFAULT '' COMMENT 'Name of the run',
ADD COLUMN IF NOT EXISTS end_time Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'End time of the run with microsecond precision',
ADD COLUMN IF NOT EXISTS run_type LowCardinality(String) DEFAULT '' COMMENT 'Type of the run',
ADD COLUMN IF NOT EXISTS parent_run_id Nullable(UUID) DEFAULT NULL COMMENT 'Parent run ID',
ADD COLUMN IF NOT EXISTS reference_example_id Nullable(UUID) DEFAULT NULL COMMENT 'Reference example ID',
ADD COLUMN IF NOT EXISTS reference_dataset_id Nullable(UUID) DEFAULT NULL COMMENT 'Reference dataset ID',
ADD COLUMN IF NOT EXISTS manifest_id Nullable(UUID) DEFAULT NULL COMMENT 'Manifest ID',
ADD COLUMN IF NOT EXISTS status LowCardinality(String) DEFAULT '' COMMENT 'Status of the run',
ADD COLUMN IF NOT EXISTS dotted_order String DEFAULT '' COMMENT 'Dotted order for hierarchical runs',
ADD COLUMN IF NOT EXISTS prompt_tokens UInt32 DEFAULT 0 COMMENT 'Number of prompt tokens',
ADD COLUMN IF NOT EXISTS completion_tokens UInt32 DEFAULT 0 COMMENT 'Number of completion tokens',
ADD COLUMN IF NOT EXISTS total_tokens UInt32 DEFAULT 0 COMMENT 'Total number of tokens',
ADD COLUMN IF NOT EXISTS first_token_time Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'Time of first token with microsecond precision',
ADD COLUMN IF NOT EXISTS received_at Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'Time of receiving the run with microsecond precision',
ADD COLUMN IF NOT EXISTS tenant_tier UInt8 DEFAULT 0 COMMENT 'Tier of the tenant',
ADD COLUMN IF NOT EXISTS inserted_at DateTime64(6, 'UTC') DEFAULT now64(6, 'UTC') COMMENT 'Time of insertion with microsecond precision',
ADD COLUMN IF NOT EXISTS prompt_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of prompt tokens',
ADD COLUMN IF NOT EXISTS completion_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of completion tokens',
ADD COLUMN IF NOT EXISTS total_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of all tokens',
ADD COLUMN IF NOT EXISTS trace_tier Nullable(String) DEFAULT NULL COMMENT 'Tier of the trace',
ADD COLUMN IF NOT EXISTS trace_ttl_seconds Nullable(UInt64) DEFAULT NULL COMMENT 'Time to live in seconds',
ADD COLUMN IF NOT EXISTS trace_first_received_at Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'Time when the parent run was first received',
ADD COLUMN IF NOT EXISTS trace_upgrade Bool DEFAULT false COMMENT 'If trace was upgraded',
ADD COLUMN IF NOT EXISTS thread_id Nullable(String) DEFAULT NULL COMMENT 'Thread ID for the run',
ADD COLUMN IF NOT EXISTS tags Array(String) DEFAULT [] COMMENT 'Tags for the run',
ADD COLUMN IF NOT EXISTS metadata_kv Map(String, String) DEFAULT map() COMMENT 'Metadata key-value pairs',
ADD COLUMN IF NOT EXISTS inputs_kv Map(String, String) DEFAULT map() COMMENT 'Inputs key-value pairs',
ADD COLUMN IF NOT EXISTS outputs_kv Map(String, String) DEFAULT map() COMMENT 'Outputs key-value pairs',
ADD COLUMN IF NOT EXISTS expiration_time Nullable(DateTime) DEFAULT NULL COMMENT 'Time when the run expires';

-- Also add the new columns to feedbacks_rmt_id table
ALTER TABLE feedbacks_rmt_id
ADD COLUMN IF NOT EXISTS name String DEFAULT '' COMMENT 'Name of the run',
ADD COLUMN IF NOT EXISTS end_time Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'End time of the run with microsecond precision',
ADD COLUMN IF NOT EXISTS run_type LowCardinality(String) DEFAULT '' COMMENT 'Type of the run',
ADD COLUMN IF NOT EXISTS parent_run_id Nullable(UUID) DEFAULT NULL COMMENT 'Parent run ID',
ADD COLUMN IF NOT EXISTS reference_example_id Nullable(UUID) DEFAULT NULL COMMENT 'Reference example ID',
ADD COLUMN IF NOT EXISTS reference_dataset_id Nullable(UUID) DEFAULT NULL COMMENT 'Reference dataset ID',
ADD COLUMN IF NOT EXISTS manifest_id Nullable(UUID) DEFAULT NULL COMMENT 'Manifest ID',
ADD COLUMN IF NOT EXISTS status LowCardinality(String) DEFAULT '' COMMENT 'Status of the run',
ADD COLUMN IF NOT EXISTS dotted_order String DEFAULT '' COMMENT 'Dotted order for hierarchical runs',
ADD COLUMN IF NOT EXISTS prompt_tokens UInt32 DEFAULT 0 COMMENT 'Number of prompt tokens',
ADD COLUMN IF NOT EXISTS completion_tokens UInt32 DEFAULT 0 COMMENT 'Number of completion tokens',
ADD COLUMN IF NOT EXISTS total_tokens UInt32 DEFAULT 0 COMMENT 'Total number of tokens',
ADD COLUMN IF NOT EXISTS first_token_time Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'Time of first token with microsecond precision',
ADD COLUMN IF NOT EXISTS received_at Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'Time of receiving the run with microsecond precision',
ADD COLUMN IF NOT EXISTS tenant_tier UInt8 DEFAULT 0 COMMENT 'Tier of the tenant',
ADD COLUMN IF NOT EXISTS inserted_at DateTime64(6, 'UTC') DEFAULT now64(6, 'UTC') COMMENT 'Time of insertion with microsecond precision',
ADD COLUMN IF NOT EXISTS prompt_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of prompt tokens',
ADD COLUMN IF NOT EXISTS completion_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of completion tokens',
ADD COLUMN IF NOT EXISTS total_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of all tokens',
ADD COLUMN IF NOT EXISTS trace_tier Nullable(String) DEFAULT NULL COMMENT 'Tier of the trace',
ADD COLUMN IF NOT EXISTS trace_ttl_seconds Nullable(UInt64) DEFAULT NULL COMMENT 'Time to live in seconds',
ADD COLUMN IF NOT EXISTS trace_first_received_at Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'Time when the parent run was first received',
ADD COLUMN IF NOT EXISTS trace_upgrade Bool DEFAULT false COMMENT 'If trace was upgraded',
ADD COLUMN IF NOT EXISTS thread_id Nullable(String) DEFAULT NULL COMMENT 'Thread ID for the run',
ADD COLUMN IF NOT EXISTS tags Array(String) DEFAULT [] COMMENT 'Tags for the run',
ADD COLUMN IF NOT EXISTS metadata_kv Map(String, String) DEFAULT map() COMMENT 'Metadata key-value pairs',
ADD COLUMN IF NOT EXISTS inputs_kv Map(String, String) DEFAULT map() COMMENT 'Inputs key-value pairs',
ADD COLUMN IF NOT EXISTS outputs_kv Map(String, String) DEFAULT map() COMMENT 'Outputs key-value pairs',
ADD COLUMN IF NOT EXISTS expiration_time Nullable(DateTime) DEFAULT NULL COMMENT 'Time when the run expires';

-- Recreate materialized views to ensure they include the new columns
-- Drop existing materialized views
DROP VIEW IF EXISTS feedbacks_rmt_mv {{ON_CLUSTER}};
DROP VIEW IF EXISTS feedbacks_rmt_id_mv {{ON_CLUSTER}};

-- Recreate materialized views with explicit column selection to ensure new columns are included
CREATE MATERIALIZED VIEW IF NOT EXISTS feedbacks_rmt_mv {{ON_CLUSTER}} TO feedbacks_rmt
AS
SELECT * FROM feedbacks;

CREATE MATERIALIZED VIEW IF NOT EXISTS feedbacks_rmt_id_mv {{ON_CLUSTER}} TO feedbacks_rmt_id
AS
SELECT * FROM feedbacks;
