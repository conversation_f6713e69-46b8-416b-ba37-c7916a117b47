-- Create a new table that combines feedbacks_rmt structure with selected run metadata columns
CREATE TABLE IF NOT EXISTS feedbacks_with_run_metadata {{ON_CLUSTER}}
(
    -- Original feedbacks_rmt columns
    id UUID,
    run_id UUID,
    session_id UUID,
    tenant_id UUID,
    is_root Bool,
    start_time DateTime64(6, 'UTC'),
    created_at DateTime64(6, 'UTC'),
    modified_at DateTime64(6, 'UTC') DEFAULT now64(6, 'UTC'),
    key String,
    score Decimal(9, 4) DEFAULT 0.,
    value String DEFAULT '',
    comment Nullable(String),
    correction Nullable(String),
    trace_id UUID,
    feedback_source String,
    is_deleted UInt8 DEFAULT 0,
    comparative_experiment_id Nullable(UUID) DEFAULT NULL,
    feedback_group_id Nullable(UUID) DEFAULT NULL,
    extra String DEFAULT '{}' COMMENT 'JSON dict of extra metadata',
    
    -- Selected run metadata columns (renamed to avoid conflicts)
    run_name String COMMENT 'Name of the run',
    run_start_time DateTime64(6, 'UTC') COMMENT 'Start time of the run with microsecond precision',
    run_end_time Nullable(DateTime64(6, 'UTC')) COMMENT 'End time of the run with microsecond precision',
    run_type LowCardinality(String) COMMENT 'Type of the run',
    parent_run_id Nullable(UUID) COMMENT 'Parent run ID',
    reference_example_id Nullable(UUID) COMMENT 'Reference example ID',
    reference_dataset_id Nullable(UUID) COMMENT 'Reference dataset ID',
    manifest_id Nullable(UUID) COMMENT 'Manifest ID',
    status LowCardinality(String) COMMENT 'Status of the run',
    dotted_order String COMMENT 'Dotted order for hierarchical runs',
    prompt_tokens UInt32 DEFAULT 0 COMMENT 'Number of prompt tokens',
    completion_tokens UInt32 DEFAULT 0 COMMENT 'Number of completion tokens',
    total_tokens UInt32 DEFAULT 0 COMMENT 'Total number of tokens',
    first_token_time Nullable(DateTime64(6, 'UTC')) COMMENT 'Time of first token with microsecond precision',
    prompt_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of prompt tokens',
    completion_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of completion tokens',
    total_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of all tokens',
    thread_id Nullable(String) DEFAULT NULL COMMENT 'Thread ID for the run',
    tags Array(String) DEFAULT [] COMMENT 'Tags for the run',
    metadata_kv Map(String, String) DEFAULT map() COMMENT 'Metadata key-value pairs',
    inputs_kv Map(String, String) DEFAULT map() COMMENT 'Inputs key-value pairs',
    outputs_kv Map(String, String) DEFAULT map() COMMENT 'Outputs key-value pairs',
    
    -- Indexes for performance
    INDEX idx_value_tokenbf value TYPE tokenbf_v1(32768, 3, 0) GRANULARITY 1,
    INDEX idx_run_id_bloom run_id TYPE bloom_filter(0.025) GRANULARITY 4,
    INDEX idx_run_name_tokenbf run_name TYPE tokenbf_v1(32768, 3, 0) GRANULARITY 1,
    INDEX idx_run_type_bloom run_type TYPE bloom_filter(0.01) GRANULARITY 1,
    INDEX idx_status_bloom status TYPE bloom_filter(0.01) GRANULARITY 1,
    INDEX sk_mm_run_start_time run_start_time TYPE minmax GRANULARITY 1,
    INDEX sk_mm_run_end_time run_end_time TYPE minmax GRANULARITY 1,
    INDEX sk_mm_modified_at modified_at TYPE minmax GRANULARITY 1
)
ENGINE = {{REPLACING_ENGINE(modified_at, is_deleted)}}
ORDER BY (tenant_id, session_id, is_root, start_time, id)
SETTINGS allow_nullable_key = 1;
