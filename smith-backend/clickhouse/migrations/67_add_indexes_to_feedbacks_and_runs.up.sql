-- Add the same indexes from feedbacks_with_run_metadata to feedbacks_rmt and runs_lite tables

-- Add indexes to feedbacks_rmt table
ALTER TABLE feedbacks_rmt
ADD INDEX IF NOT EXISTS idx_value_tokenbf value TYPE tokenbf_v1(32768, 3, 0) GRANULARITY 1,
ADD INDEX IF NOT EXISTS idx_run_id_bloom run_id TYPE bloom_filter(0.025) GRANULARITY 4,
ADD INDEX IF NOT EXISTS sk_mm_modified_at modified_at TYPE minmax GRANULARITY 1;

-- Add indexes to runs_lite table  
ALTER TABLE runs_lite
ADD INDEX IF NOT EXISTS idx_value_tokenbf name TYPE tokenbf_v1(32768, 3, 0) GRANULARITY 1,
ADD INDEX IF NOT EXISTS idx_run_id_bloom id TYPE bloom_filter(0.025) GRANULARITY 4,
ADD INDEX IF NOT EXISTS idx_run_type_bloom run_type T<PERSON><PERSON> bloom_filter(0.01) GRANULARITY 1,
ADD INDEX IF NOT EXISTS idx_status_bloom status TYPE bloom_filter(0.01) GRANULARITY 1,
ADD INDEX IF NOT EXISTS sk_mm_run_start_time start_time TYPE minmax GRANULARITY 1,
ADD INDEX IF NOT EXISTS sk_mm_run_end_time end_time TYPE minmax GRANULARITY 1;
